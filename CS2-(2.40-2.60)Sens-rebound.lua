-------------------------------One click mouse side button key ------------------------
ak47key = 4 	------ak47
galilkey = 0	------galil
sg553key = 0	------sg553
m4a1key = 5	------m4a1
m4a4key = 0	------m4a4
famaskey = 7	------famas
augkey = 0	------aug
mp9key = 0	------mp9
mac10key =0	------mac10
ump45key =0	------ump45
mp7key = 0	------mp7
mp5sdkey =0	------mp5sd
bizonkey = 0	------bizon
p90key = 8	------p90
cz75key = 0	------cz75
m249key =  0	------m249

-------------------------------Double-click mouse side button key ------------------------
ak47key2 = 0	------ak47
galilkey2 = 0	------galil
sg553key2 = 6	------sg553
m4a1key2 = 0	------m4a1
m4a4key2 = 5	------m4a4
famaskey2 = 0	------famas
augkey2 = 7	------aug
mp9key2 = 0	------mp9
mac10key2 =0	------mac10
ump45key2 =  0	------ump45
mp7key2 = 0	------mp7
mp5sdkey2 =0	------mp5sd
bizonkey2 = 0	------bizon
p90key2 = 0	------p90
cz75key2 = 0	------cz75
m249key2 = 0	------m249


-------------------------------Ctrl + mouse side button key ------------------------
ak47_CTRL_key = 0
galil_CTRL_key = 0
sg553_CTRL_key = 0
m4a1_CTRL_key = 0
m4a4_CTRL_key = 0
famas_CTRL_key = 0
aug_CTRL_key = 0
mp9_CTRL_key = 0
mac10_CTRL_key = 0
ump45_CTRL_key = 0
mp7_CTRL_key = 0
mp5sd_CTRL_key = 0
bizon_CTRL_key = 0
p90_CTRL_key = 0
cz75_CTRL_key = 0
m249_CTRL_key = 0
------------------------------------turn off ------------------------------------------
offkey = 6

ak47 = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=-1, y=3, d=8}, {x=-1, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=-1, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=3, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=9}, {x=2, y=1, d=9}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-4, y=0, d=8}, {x=-4, y=0, d=8}, {x=-4, y=0, d=8}, {x=-4, y=0, d=8}, {x=-4, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=-1, d=8}, {x=-3, y=-1, d=8}, {x=-3, y=-1, d=8}, {x=-3, y=-1, d=9}, {x=-3, y=-1, d=9}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=2, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=1, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=-1, d=9}, {x=3, y=-1, d=9}, {x=3, y=-1, d=9}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=9}, {x=3, y=-1, d=9}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-4, y=-1, d=8}, {x=-4, y=-1, d=8}, {x=-4, y=-1, d=8}, {x=-4, y=-1, d=8}, {x=-4, y=-2, d=8}, {x=-4, y=-2, d=8}, {x=-4, y=-2, d=8}, {x=-4, y=-2, d=8}, {x=-4, y=-2, d=8}, {x=-4, y=-2, d=9}, {x=-3, y=-2, d=9}, {x=-3, y=-2, d=9}, {x=-2, y=0, d=9}, {x=-2, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}}
aug = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=2, y=0, d=9}, {x=-1, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-3, y=0, d=8}, {x=-3, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=9}, {x=3, y=-1, d=9}, {x=3, y=-1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=9}, {x=2, y=-1, d=9}, {x=3, y=-1, d=9}, {x=3, y=-1, d=9}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=-1, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=10}, {x=0, y=0, d=10}}
bizon = {{x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-2, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-2, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-3, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}}
cz75 = {{x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-1, y=1, d=9}, {x=0, y=3, d=8}, {x=0, y=3, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=3, y=2, d=8}, {x=3, y=2, d=9}, {x=3, y=2, d=9}, {x=3, y=2, d=9}, {x=-3, y=3, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-3, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=9}, {x=-2, y=2, d=9}, {x=-2, y=2, d=9}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-4, y=2, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=8}, {x=-3, y=1, d=9}, {x=-3, y=1, d=9}, {x=-3, y=1, d=9}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=8}, {x=2, y=2, d=9}, {x=2, y=1, d=9}, {x=2, y=1, d=9}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-2, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=1, d=9}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}}
famas = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-1, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=2, y=1, d=8}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=-1, y=2, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-2, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=8}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=-1, d=8}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=8}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=1, y=-1, d=10}, {x=2, y=-1, d=10}, {x=2, y=-1, d=10}, {x=2, y=-1, d=10}, {x=2, y=-1, d=10}}
galil = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=2, y=1, d=5}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=2, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-3, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=0, y=-1, d=6}, {x=0, y=-1, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=-1, d=8}, {x=0, y=-1, d=8}}
m4a1 = {{x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=9}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=9}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=9}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-2, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=1, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-1, y=-1, d=9}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=-1, d=8}, {x=0, y=-1, d=9}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=0, y=-1, d=9}, {x=0, y=-1, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=9}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=9}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=0, y=1, d=10}, {x=0, y=1, d=10}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=11}, {x=1, y=0, d=11}}
m4a4 = {{x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=2, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=-1, y=3, d=7}, {x=-1, y=3, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=1, y=2, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=2, y=1, d=8}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=0, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=-2, y=-1, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=-1, d=8}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=0, d=8}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=3, y=0, d=8}, {x=3, y=-1, d=8}, {x=3, y=-1, d=8}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=-1, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}}
m249 = {{x=0, y=0, d=1}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-2, y=3, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=7}, {x=-2, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-2, y=3, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=6}, {x=-2, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=0, y=3, d=6}, {x=0, y=3, d=6}, {x=0, y=3, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=7}, {x=2, y=2, d=7}, {x=2, y=2, d=7}, {x=2, y=2, d=7}, {x=2, y=2, d=7}, {x=2, y=2, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=2, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=3, y=1, d=7}, {x=1, y=2, d=6}, {x=2, y=2, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=3, y=0, d=6}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=3, y=-1, d=7}, {x=3, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-2, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-3, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=7}, {x=-3, y=-1, d=7}, {x=-3, y=-1, d=7}, {x=-3, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-2, d=7}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-2, d=7}, {x=-1, y=-2, d=7}, {x=-1, y=-2, d=7}, {x=-1, y=-2, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=1, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=6}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-3, y=0, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=0, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=3, y=0, d=6}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=0, d=7}, {x=3, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=3, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=1, y=1, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}}
mac10 = {{x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=0, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-3, y=0, d=4}, {x=-3, y=0, d=4}, {x=-3, y=0, d=4}, {x=-3, y=0, d=4}, {x=-3, y=0, d=4}, {x=-2, y=0, d=4}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-2, y=-1, d=5}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=-1, d=7}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=-1, d=7}, {x=2, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}}
mp5sd = {{x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-2, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}}
mp7 = {{x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=4}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=-1, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=-1, d=7}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=-1, d=7}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=1, y=0, d=8}}
mp9 = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=-1, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=2, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=-1, y=2, d=5}, {x=-1, y=2, d=5}, {x=-1, y=2, d=5}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=2, y=1, d=5}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=1, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=5}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=3, y=-1, d=6}, {x=-1, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-2, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-2, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-2, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-3, y=0, d=5}, {x=-3, y=0, d=5}, {x=-3, y=-1, d=5}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-3, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-3, y=0, d=5}, {x=-3, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-2, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}}
p90 = {{x=0, y=0, d=1}, {x=0, y=0, d=1}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=-1, y=2, d=5}, {x=-1, y=2, d=5}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=2, d=5}, {x=-1, y=2, d=5}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=2, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=2, d=5}, {x=0, y=2, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=0, y=2, d=5}, {x=0, y=1, d=5}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=1, y=1, d=6}, {x=1, y=1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-2, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=-1, d=6}, {x=1, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=1, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=0, y=1, d=5}, {x=1, y=1, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=-1, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-2, y=0, d=5}, {x=-2, y=0, d=5}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-2, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=1, d=6}, {x=0, y=1, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=1, d=5}, {x=0, y=1, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-2, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=-1, y=0, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=-1, d=6}, {x=0, y=1, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=2, y=-1, d=6}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=2, y=0, d=6}, {x=0, y=1, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=0, y=0, d=5}, {x=1, y=0, d=5}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=0, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=-1, y=1, d=5}, {x=-1, y=0, d=5}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=-1, y=1, d=5}, {x=-1, y=1, d=5}, {x=-1, y=1, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=-1, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=5}, {x=0, y=0, d=5}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=0, y=0, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}, {x=1, y=-1, d=6}}
sg553 = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=-2, y=2, d=9}, {x=-2, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=10}, {x=-1, y=1, d=10}, {x=-1, y=1, d=10}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=10}, {x=0, y=1, d=10}, {x=-1, y=3, d=9}, {x=-1, y=3, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=10}, {x=0, y=2, d=10}, {x=0, y=2, d=10}, {x=-1, y=3, d=9}, {x=-1, y=3, d=9}, {x=-1, y=3, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=10}, {x=0, y=2, d=10}, {x=0, y=2, d=10}, {x=-1, y=3, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=-1, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=9}, {x=0, y=2, d=10}, {x=0, y=2, d=10}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=1, y=2, d=9}, {x=1, y=2, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=2, y=1, d=9}, {x=2, y=1, d=9}, {x=2, y=1, d=9}, {x=2, y=1, d=10}, {x=2, y=1, d=10}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=-2, y=1, d=9}, {x=-2, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=10}, {x=0, y=0, d=10}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=-2, y=0, d=10}, {x=-2, y=0, d=10}, {x=-2, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=-1, d=10}, {x=-1, y=-1, d=10}, {x=-1, y=-1, d=10}, {x=-1, y=-1, d=11}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-2, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-2, d=10}, {x=-1, y=-2, d=10}, {x=-2, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=9}, {x=-1, y=-1, d=10}, {x=-1, y=-1, d=10}, {x=-1, y=-1, d=10}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=-1, d=8}, {x=3, y=1, d=9}, {x=3, y=1, d=9}, {x=3, y=1, d=9}, {x=3, y=1, d=9}, {x=3, y=1, d=9}, {x=3, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=10}, {x=4, y=0, d=10}, {x=4, y=0, d=10}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=0, d=9}, {x=4, y=-1, d=9}, {x=4, y=-1, d=10}, {x=5, y=-1, d=10}, {x=5, y=-1, d=10}, {x=2, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=0, d=9}, {x=3, y=-1, d=9}, {x=3, y=-1, d=9}, {x=3, y=-1, d=10}, {x=3, y=-1, d=10}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=9}, {x=1, y=1, d=10}, {x=1, y=0, d=10}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=1, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=9}, {x=0, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=10}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=1, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=10}, {x=2, y=-1, d=10}, {x=2, y=-1, d=10}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=0, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=9}, {x=2, y=-1, d=10}, {x=3, y=-1, d=10}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-2, y=1, d=9}, {x=-2, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=1, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=9}, {x=-1, y=0, d=10}, {x=-1, y=0, d=10}, {x=-4, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=0, d=9}, {x=-3, y=-1, d=9}, {x=-3, y=-1, d=10}, {x=-3, y=-1, d=10}, {x=-3, y=-1, d=10}}
ump45 = {{x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=2}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=3}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=7}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=-1, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=1, d=8}, {x=-1, y=3, d=7}, {x=-1, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=7}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=8}, {x=0, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=2, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=1, d=8}, {x=0, y=2, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=8}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=2, y=1, d=7}, {x=2, y=1, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=1, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=8}, {x=1, y=0, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=1, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=-1, d=8}, {x=0, y=-1, d=8}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=-1, d=7}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=1, y=-1, d=8}, {x=2, y=-1, d=8}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=1, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=7}, {x=2, y=0, d=8}, {x=2, y=0, d=8}, {x=2, y=-1, d=8}, {x=2, y=-1, d=8}, {x=-1, y=1, d=7}, {x=-1, y=1, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=7}, {x=-2, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=0, d=8}, {x=-1, y=-1, d=8}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=-1, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=7}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=0, d=8}, {x=0, y=-1, d=8}}



ShieldCode = "Lalt"
Second="lctrl"
mode = 2		
round = 40	
speed = 2 	
time1 = 25	
time2 = 35	
wireless = 1 	






indexWeapon = 0
indexPattern = 1
temp = 0
backx = 0
backy = 0
backx1 = 0.00
backy1 = 0.00
backx2 = 0
backy2 = 0
backx3 = 0
backy3 = 0
tsleep = 0.00
tsleep2 = 0
tsleep3 = 0.00
temptime = 0
click = false
i = 0
flag = 0
flag2 = 1
timestart = 0
timebegin = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
timeend = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
dp={233,151,178,233,177,188,239,188,232,128,129,107,231,154,132,229,174,143}
hong = {50,48,50,52,229,185,180,49,48,230,156,136,230,156,128,230,150,176,227,128,129,229,165,189,232,175,132,229,140,133,230,155,180,230,150,176}
zz = {228,189,156,232,128,133,32,232,128,129,107}ms = {229,189,147,229,137,141,230,173,166,229,153,168,239,188,154} 
Wpattern = {ak47, galil, sg553, m4a4, m4a1, famas, aug, mp9, mac10, ump45, mp7, mp5sd, bizon, p90, cz75, m249}
Wkey1 = {ak47key, galilkey, sg553key, m4a4key, m4a1key, famaskey, augkey, mp9key, mac10key, ump45key, mp7key, mp5sdkey, bizonkey, p90key, cz75key, m249key, autoshoot}
Wkey2 = {ak47key2, galilkey2, sg553key2, m4a4key2, m4a1key2, famaskey2, augkey2, mp9key2, mac10key2, ump45key2, mp7key2, mp5sdkey2, bizonkey2, p90key2, cz75key2, m249key2, autoshoot2}
Wkey3 = {ak47_CTRL_key,galil_CTRL_key,sg553_CTRL_key,m4a1_CTRL_key,m4a4_CTRL_key,famas_CTRL_key,aug_CTRL_key,mp9_CTRL_key,mac10_CTRL_key,ump45_CTRL_key,mp7_CTRL_key,mp5sd_CTRL_key,bizon_CTRL_key,p90_CTRL_key,cz75_CTRL_key,m249_CTRL_key}

EnablePrimaryMouseButtonEvents(true)
function OnEvent(event, arg)
	if (wireless) then
		Sleep(1)
	end
	while IsMouseButtonPressed(1) and flag2 == 1 do
		if (indexWeapon == 17) then
			click = true
			SetMKeyState(3)
			break
		end
		if (indexWeapon > 0 and indexWeapon < 17 and not IsModifierPressed(ShieldCode)) then
			if indexPattern < #Wpattern[indexWeapon] then
				if indexPattern == 1 then
					timestart = GetRunningTime()
				end		
				MoveMouseRelative(Wpattern[indexWeapon][indexPattern].x, Wpattern[indexWeapon][indexPattern].y)
				backx = backx - Wpattern[indexWeapon][indexPattern].x
				backy = backy - Wpattern[indexWeapon][indexPattern].y
				timestart = timestart + Wpattern[indexWeapon][indexPattern].d
				Sleep3(timestart)
				indexPattern = indexPattern + 1
			else
				backx2 = math.floor(backx / 40)
				backy2 = math.floor(backy / 40)
				i = 0
				while (i < round) do
					tsleep = 2* math.sqrt(math.abs(backx) * math.abs(backx) + math.abs(backy) * math.abs(backy)) / round / speed
					tsleep2 = math.floor(tsleep)
					tsleep3 = tsleep3 + tsleep - tsleep2
					if tsleep3 >= 1 then
						tsleep3 = tsleep3 - 1
						tsleep2 = tsleep2 + 1
					end
					Sleep2(tsleep2)
					backx1 = backx1 + backx / 40 - backx2
					backy1 = backy1 + backy / 40 - backy2
					if (backx1 >= 1) then
						backx1 = backx1 - 1
						backx3 = backx2 + 1
					else
						backx3 = backx2
					end
					if (backy1 >= 1) then
						backy1 = backy1 - 1
						backy3 = backy2 + 1
					else
						backy3 = backy2
					end
					MoveMouseRelative(backx3, backy3)
					i = i + 1
				end
				indexPattern = 1
				backx = 0
				backy = 0
				backx1 = 0.00
				backy1 = 0.00
				tsleep3 = 0.00	
				flag2 = 0				
				Sleep(1000)
			end
		end
	end
	if (event == "MOUSE_BUTTON_RELEASED" and arg == 1 and indexWeapon == 17) then
		click = false
	end
	if (event == "M_PRESSED" and arg == 3 and click and indexWeapon == 17) then
		PressAndReleaseMouseButton(1)
		if click then
			SetMKeyState(3)
			math.randomseed(GetRunningTime())
			temptime = math.random(time1, time2)
			Sleep(temptime)
		end
	end
	if (event == "MOUSE_BUTTON_RELEASED" and indexWeapon == 0) then
		if (mode == 3 and arg == offkey) then
			if not IsKeyLockOn("scrolllock") then
				PressAndReleaseKey("scrolllock")
			end
			indexWeapon = temp
			ClearLog()
			OutputLogMessage("on\n")
		end
	end
	if (event == "MOUSE_BUTTON_RELEASED" and indexWeapon ~= 17 and indexWeapon ~= 0) then
		if (arg == 1) then
			backx2 = math.floor(backx / 40)
			backy2 = math.floor(backy / 40)
			i = 0
			while (i < round and (backx2 ~= 0 or backy2 ~= 0)) do
				tsleep = (150 + 1.1 * math.sqrt(math.abs(backx) * math.abs(backx) + math.abs(backy) * math.abs(backy))) / round / speed
				tsleep2 = math.floor(tsleep)
				tsleep3 = tsleep3 + tsleep - tsleep2
				if tsleep3 >= 1 then
					tsleep3 = tsleep3 - 1
					tsleep2 = tsleep2 + 1
				end
				Sleep2(tsleep2)
				backx1 = backx1 + backx / 40 - backx2
				backy1 = backy1 + backy / 40 - backy2
				if (backx1 >= 1) then
					backx1 = backx1 - 1.00
					backx3 = backx2 + 1
				else
					backx3 = backx2
				end
				if (backy1 >= 1) then
					backy1 = backy1 - 1.00
					backy3 = backy2 + 1
				else
					backy3 = backy2
				end
				MoveMouseRelative(backx3, backy3)
				i = i + 1
			end
			indexPattern = 1
			backx = 0
			backy = 0
			backx1 = 0.00
			backy1 = 0.00
			tsleep3 = 0.00	
			flag2 = 1
		end
	end
	if (event == "MOUSE_BUTTON_PRESSED") then
		if (arg ~= 1) then
			if (arg == Wkey3[1] and IsModifierPressed(Second))then
				indexWeapon = 1
				temp = 1
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" AK47")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
			
			elseif (arg == Wkey1[1]) then
				indexWeapon = 1
				temp = 1
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" AK47")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


				
			end
			if (arg == Wkey3[2] and IsModifierPressed(Second))then
				indexWeapon = 2
				temp = 2
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" GALIL")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
			elseif (arg == Wkey1[2]) then
				indexWeapon = 2
				temp = 2
				flag = 1
				ClearLog()

OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" GALIL")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


		
				
			end
			if (arg == Wkey3[3] and IsModifierPressed(Second))then
				indexWeapon = 3
				temp = 3
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" SG553")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
			elseif (arg == Wkey1[3]) then
				indexWeapon = 3
				temp = 3
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" SG553")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


			end
			
			if (arg == Wkey3[4] and IsModifierPressed(Second))then
				indexWeapon = 4
				temp = 4
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MAA4")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
					
			elseif (arg == Wkey1[4]) then
				indexWeapon = 4
				temp = 4
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M4A4")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


			end
			if (arg == Wkey3[5] and IsModifierPressed(Second))then
				indexWeapon = 5
				temp = 5
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" M4A1")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
		
			elseif (arg == Wkey1[5]) then
				indexWeapon = 5
				temp = 5
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M4A1")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


			end
			if (arg == Wkey3[6] and IsModifierPressed(Second))then
				indexWeapon = 6
				temp = 6
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" FAMAS")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[6]) then
				indexWeapon = 6
				temp = 6
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" FAMAS")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")


			end
			if (arg == Wkey3[7] and IsModifierPressed(Second))then
				indexWeapon = 7
				temp = 7
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" AUG")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[7]) then
				indexWeapon = 7
				temp = 7
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" AUG")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[8] and IsModifierPressed(Second))then
				indexWeapon = 8
				temp = 8
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" SG553")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[8]) then
				indexWeapon = 8
				temp = 8
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP9")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[9] and IsModifierPressed(Second))then
				indexWeapon = 9
				temp = 9
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" MAC10")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
		
			elseif (arg == Wkey1[9]) then
				indexWeapon = 9
				temp = 9
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MAC10")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[10] and IsModifierPressed(Second))then
				indexWeapon = 10
				temp = 10
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" UMP45")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[10]) then
				indexWeapon = 10
				temp = 10
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" UMP45")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[11] and IsModifierPressed(Second))then
				indexWeapon = 11
				temp = 11
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" MP7")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[11]) then
				indexWeapon = 11
				temp = 11
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP7")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[12] and IsModifierPressed(Second))then
				indexWeapon = 12
				temp = 12
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" MP5SD")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[12]) then
				indexWeapon = 12
				temp = 12
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP5SD")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[13] and IsModifierPressed(Second))then
				indexWeapon = 13
				temp = 13
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" BIZON")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[13]) then
				indexWeapon = 13
				temp = 13
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" BIZON")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[14] and IsModifierPressed(Second))then
				indexWeapon = 14
				temp = 14
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" P90 RUSHB")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[14]) then
				indexWeapon = 14
				temp = 14
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" P90 RUSHB")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[15] and IsModifierPressed(Second))then
				indexWeapon = 15
				temp = 15
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" cz75")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[15]) then
				indexWeapon = 15
				temp = 15
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" CZ75")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey3[16] and IsModifierPressed(Second))then
				indexWeapon = 16
				temp = 16
				flag = 1
				ClearLog()
				OutputLogMessage("-----------------------------------------------------------------------------------------\n")
				OutputLogMessage("-----| ")
				
				Output(dp)
				OutputLogMessage(" ")
				Output(hong)
				OutputLogMessage("|-----| ")
				Output(zz)


				OutputLogMessage(" |----\n")
				OutputLogMessage("--------------------------|")
				Output(ms)
				OutputLogMessage(" M249")
				OutputLogMessage("|-------------------------\n")
				OutputLogMessage("--------------------------------------------------------------------------------------\n")

					
				
						
					
			elseif (arg == Wkey1[16]) then
				indexWeapon = 16
				temp = 16
				flag = 1
				ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M249")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
			end
			if (arg == Wkey1[17]) then
				indexWeapon = 17
				temp = 17
				flag = 1
				ClearLog()
				OutputLogMessage("autotap\n")
			end
			if (arg == Wkey2[1]) then
				timeend[1] = GetRunningTime()
				if (timeend[1] - timebegin[1]) < 300 then
					indexWeapon = 1
					temp = 1
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)


OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" AK47")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[1] = GetRunningTime()
			end
			if (arg == Wkey2[2]) then
				timeend[2] = GetRunningTime()
				if (timeend[2] - timebegin[2]) < 400 then
					indexWeapon = 2
					temp = 2
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" GALIL")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[2] = GetRunningTime()
			end
			if (arg == Wkey2[3]) then
				timeend[3] = GetRunningTime()
				if (timeend[3] - timebegin[3]) < 400 then
					indexWeapon = 3
					temp = 3
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" SG553")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[3] = GetRunningTime()
			end
			if (arg == Wkey2[4]) then
				timeend[4] = GetRunningTime()
				if (timeend[4] - timebegin[4]) < 400 then
					indexWeapon = 4
					temp = 4
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M4A4")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[4] = GetRunningTime()
			end
			if (arg == Wkey2[5]) then
				timeend[5] = GetRunningTime()
				if (timeend[5] - timebegin[5]) < 400 then
					indexWeapon = 5
					temp = 5
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M4A1")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[5] = GetRunningTime()
			end
			if (arg == Wkey2[6]) then
				timeend[6] = GetRunningTime()
				if (timeend[6] - timebegin[6]) < 400 then
					indexWeapon = 6
					temp = 6
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" FAMAS")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[6] = GetRunningTime()
			end
			if (arg == Wkey2[7]) then
				timeend[7] = GetRunningTime()
				if (timeend[7] - timebegin[7]) < 400 then
					indexWeapon = 7
					temp = 7
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" AUG")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[7] = GetRunningTime()
			end
			if (arg == Wkey2[8]) then
				timeend[8] = GetRunningTime()
				if (timeend[8] - timebegin[8]) < 400 then
					indexWeapon = 8
					temp = 8
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP9")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[8] = GetRunningTime()
			end
			if (arg == Wkey2[9]) then
				timeend[9] = GetRunningTime()
				if (timeend[9] - timebegin[9]) < 400 then
					indexWeapon = 9
					temp = 9
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MAC10")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[9] = GetRunningTime()
			end
			if (arg == Wkey2[10]) then
				timeend[10] = GetRunningTime()
				if (timeend[10] - timebegin[10]) < 400 then
					indexWeapon = 10
					temp = 10
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" UMP45")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[10] = GetRunningTime()
			end
			if (arg == Wkey2[11]) then
				timeend[11] = GetRunningTime()
				if (timeend[11] - timebegin[11]) < 400 then
					indexWeapon = 11
					temp = 11
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP7")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[11] = GetRunningTime()
			end
			if (arg == Wkey2[12]) then
				timeend[12] = GetRunningTime()
				if (timeend[12] - timebegin[12]) < 400 then
					indexWeapon = 12
					temp = 12
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" MP5SD")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[12] = GetRunningTime()
			end
			if (arg == Wkey2[13]) then
				timeend[13] = GetRunningTime()
				if (timeend[13] - timebegin[13]) < 400 then
					indexWeapon = 13
					temp = 13
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" BIZON")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[13] = GetRunningTime()
			end
			if (arg == Wkey2[14]) then
				timeend[14] = GetRunningTime()
				if (timeend[14] - timebegin[14]) < 400 then
					indexWeapon = 14
					temp = 14
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" P90 RUSHB")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[14] = GetRunningTime()
			end
			if (arg == Wkey2[15]) then
				timeend[15] = GetRunningTime()
				if (timeend[15] - timebegin[15]) < 400 then
					indexWeapon = 15
					temp = 15
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" CZ75")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[15] = GetRunningTime()
			end
			if (arg == Wkey2[16]) then
				timeend[16] = GetRunningTime()
				if (timeend[16] - timebegin[16]) < 400 then
					indexWeapon = 16
					temp = 16
					flag = 1
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" M249")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
				timebegin[16] = GetRunningTime()
			end
			if (arg == Wkey2[17]) then
				timeend[17] = GetRunningTime()
				if (timeend[17] - timebegin[17]) < 400 then
					indexWeapon = 17
					temp = 17
					flag = 1
					ClearLog()
					OutputLogMessage("autotap\n")
				end
				timebegin[17] = GetRunningTime()
			end
			if (not IsKeyLockOn("scrolllock")) and flag == 1 then
				PressAndReleaseKey("scrolllock")
				flag = 0
			end
			if (arg == offkey) then
				if mode==0 then
					timeend[18] = GetRunningTime()
					if (timeend[18] - timebegin[18] < 400) then
						if not IsKeyLockOn("scrolllock") then
							PressAndReleaseKey("scrolllock")
						end
						indexWeapon = temp
						ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" ON")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
					else
						if IsKeyLockOn("scrolllock") then
							PressAndReleaseKey("scrolllock")
						end
						indexWeapon = 0
						ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" OFF")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
					end
					timebegin[18] = GetRunningTime()
				end
				if mode==1 then
					if indexWeapon~=0 then
						if IsKeyLockOn("scrolllock") then
							PressAndReleaseKey("scrolllock")
						end
						indexWeapon = 0
						ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)
OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" OFF")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
					else
						if not IsKeyLockOn("scrolllock") then
							PressAndReleaseKey("scrolllock")
						end
						indexWeapon = temp
						ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" ON")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
					end
				end
				if (mode==2 or mode==3) then
					if IsKeyLockOn("scrolllock") then
						PressAndReleaseKey("scrolllock")
					end
					indexWeapon = 0
					ClearLog()
OutputLogMessage("-----------------------------------------------------------------------------------------\n")
OutputLogMessage("-----| ")
				
Output(dp)
OutputLogMessage(" ")
Output(hong)
OutputLogMessage("|-----| ")
Output(zz)

OutputLogMessage(" |----\n")
OutputLogMessage("--------------------------|")
Output(ms)
OutputLogMessage(" OFF")
OutputLogMessage("|-------------------------\n")
OutputLogMessage("--------------------------------------------------------------------------------------\n")
				end
			end
			Sleep(100)
		end
	end
end

function Sleep2(time)
	start = GetRunningTime()
	while (time + start > GetRunningTime())
	do
	end
end

function Sleep3(time)
	while (time > GetRunningTime())
	do
	end
end
function Output(dp)
        local b = ""
        for i = 1,#dp do
                b = b .. string.char(dp[i])
        end
        OutputLogMessage(b)
end

function Output(zz)
        local b = ""
        for i = 1,#zz do
                b = b .. string.char(zz[i])
        end
        OutputLogMessage(b)
end

function Output(ms)
        local b = ""
        for i = 1,#ms do
                b = b .. string.char(ms[i])
        end
        OutputLogMessage(b)
end

function Output(hong)
        local b = ""
        for i = 1,#hong do
                b = b .. string.char(hong[i])
        end
        OutputLogMessage(b)
end
